using System.Collections;
using Il2Cpp;
using HarmonyLib;
using MelonLoader;
using UnityEngine;
using UnityEngine.SceneManagement;

namespace TestLE.Patches;

[HarmonyPatch(typeof(MinimapFogOfWar), "Initialize", typeof(MinimapFogOfWar.QuadScale), typeof(Vector3))]
public class Patch_MinimapFogOfWar_Initialize
{
    public static void Postfix(MinimapFogOfWar __instance) => Patch_MinimapFogOfWar.Postfix(__instance);
}

[HarmonyPatch(typeof(MinimapFogOfWar), "OnActiveSceneChanged", typeof(Scene), typeof(Scene))]
public class Patch_MinimapFogOfWar_OnActiveSceneChanged
{
    public static void Postfix(MinimapFogOfWar __instance) => Patch_MinimapFogOfWar.Postfix(__instance);
}

public static class Patch_MinimapFogOfWar
{
    public static void Postfix(MinimapFogOfWar __instance)
    {
        MelonCoroutines.Start(Wait(__instance));
    }

    private static IEnumerator Wait(MinimapFogOfWar __instance)
    {
        while (PLAYER == null)
            yield return new WaitForSeconds(0.2f);

        MelonLogger.Msg("Fog of war initialized!");
        __instance.DrawRadiusAtPosition(Vector3.zero, 10000);
        // __instance.DrawRadiusAtPosition(PLAYER.transform.position, 10000);
    }
}
