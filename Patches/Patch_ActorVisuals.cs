using Il2Cpp;
using HarmonyLib;
using TestLE.Utilities;

namespace TestLE.Patches;

[HarmonyPatch(typeof(ActorVisuals), "initialiseAlignment")]
public class Patch_ActorVisuals_initialiseAlignment
{
    public static void Postfix(ActorVisuals __instance)
    {
        if (__instance.alignment == null)
            return;

        if (__instance.alignment.isFoe[0])
            _ = new Enemy(__instance, MinimapHelpers.CreateIconForActorDisplayInformation(__instance.gameObject.GetComponent<ActorDisplayInformation>()));
    }
}

[HarmonyPatch(typeof(ActorVisuals), "OnDestroy")]
public class Patch_ActorVisuals_OnDestroy
{
    public static void Postfix(ActorVisuals __instance)
    {
        for (var i = 0; i < ENEMIES.Count; i++)
        {
            var enemy = ENEMIES[i];
            if (enemy == null)
            {
                ENEMIES.RemoveAt(i);
                i--;
                continue;
            }

            if (enemy.Data == __instance)
            {
                enemy.RemoveEnemy();
                i--;
            }
        }
    }
}
