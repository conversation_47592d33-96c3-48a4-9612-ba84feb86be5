using System.Collections;
using HarmonyLib;
using Il2Cpp;
using Il2CppLE.Interactions.KeyProviders;
using MelonLoader;
using TestLE.Utilities;
using UnityEngine;

namespace TestLE.Patches;

[HarmonyPatch(typeof(DungeonExitKeyProvider))]
public class Patch_DungeonExitKeyProvider
{
    [HarmonyPostfix]
    [HarmonyPatch("GetMessageKey")]
    private static void GetMessageKey_Postfix(DungeonExitKeyProvider __instance)
    {
        MelonCoroutines.Start(Wait(__instance));
    }

    private static IEnumerator Wait(DungeonExitKeyProvider __instance)
    {
        yield return new WaitForSeconds(3f);
        if (__instance.isActiveAndEnabled)
        {
            MelonLogger.Msg("Dungeon Exit");
            MinimapHelpers.CreateIconForDungeonExit(__instance);
        }
    }
}
