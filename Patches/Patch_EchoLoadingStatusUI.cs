using System.Collections;
using HarmonyLib;
using Il2Cpp;
using Il2CppDMM;
using MelonLoader;
using TestLE.Utilities;
using UnityEngine;

namespace TestLE.Patches;

[HarmonyPatch(typeof(EchoLoadingStatusUI), "OnEchoLoadingFinished")]
public class Patch_EchoLoadingStatusUI_OnEchoLoadingFinished
{
    public static void Postfix(EchoLoadingStatusUI __instance)
    {
        MelonLogger.Msg("EchoLoadingStatusUI OnEchoLoadingFinished");

        MelonCoroutines.Start(Wait(__instance));
    }

    private static IEnumerator Wait(EchoLoadingStatusUI __instance)
    {
        yield return new WaitForSeconds(1f);

        var shrinesToCheck = new List<(string name, GameObject shrine, Transform shrineRoot)>();

        var shrines = FindHelpers.FindShrines();
        foreach (var s in shrines)
        {
            var shrineName = s.transform.parent.name.Replace("(Clone)", "");

            MelonLogger.Msg($"Shrine: {shrineName}");
            var minimapIcon = s.GetShrineMinimapIcon();
            if (minimapIcon != null)
            {
                minimapIcon.SetActive(true);
                shrinesToCheck.Add((shrineName, minimapIcon, s.transform.parent));
            }
        }

        INTERACTABLES.Clear();
        INTERACTABLES.AddRange(FindHelpers.FindInteractables());

        GOOD_SHRINES.Clear();

        // foreach (var i in INTERACTABLES)
        // {
        //     MelonLogger.Msg($"Interactable: {i.gameObject.name}");
        // }

        yield return new WaitForSeconds(1f);

        foreach (var (name, shrine, shrineRoot) in shrinesToCheck)
        {
            var img = shrine.GetComponent<DMMapIcon>().img;
            switch (name)
            {
                // case "Gold Shrine":
                //     img.sprite = null;
                //     img.color = new Color32(255, 215, 0, 255);
                //     GOOD_SHRINES.Add(shrineRoot.GetComponent<WorldObjectClickListener>());
                //     break;
                case "Unique Shrine":
                    img.sprite = null;
                    img.color = new Color32(200, 100, 50, 255);
                    GOOD_SHRINES.Add(shrineRoot.GetComponent<WorldObjectClickListener>());
                    break;
                case "Idol Shrine":
                    img.sprite = null;
                    img.color = new Color32(0, 255, 255, 255);
                    GOOD_SHRINES.Add(shrineRoot.GetComponent<WorldObjectClickListener>());
                    break;
            }
        }

        foreach (var s in GOOD_SHRINES)
        {
            MelonLogger.Msg($"Good Shrine: {s.gameObject.name}");
        }
    }
}
